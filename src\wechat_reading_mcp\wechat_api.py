"""
微信读书API接口封装

提供与微信读书平台交互的API接口。
"""

import asyncio
import json
import re
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

import aiohttp
from loguru import logger

from .config import Config


class WeChatReadingAPI:
    """微信读书API客户端"""
    
    # 微信读书API基础URL
    BASE_URL = "https://weread.qq.com"
    
    # API端点
    ENDPOINTS = {
        "shelf": "/web/shelf",  # 书架
        "shelf_book": "/web/shelf/book",  # 书架书籍列表
        "shelf_sync": "/web/shelf/sync",  # 书架同步
        "book_info": "/web/book/info",  # 书籍信息
        "chapter_info": "/web/book/chapterInfos",  # 章节信息
        "notes": "/web/book/bookmarklist",  # 笔记和划线
        "search": "/web/search/global",  # 搜索
        "reading_time": "/web/book/readingTime",  # 阅读时间
        "progress": "/web/book/readingProgress",  # 阅读进度
    }
    
    def __init__(self, config: Config):
        """初始化API客户端"""
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self._user_id: Optional[str] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def _ensure_session(self):
        """确保HTTP会话已创建"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.config.request_timeout)
            self.session = aiohttp.ClientSession(
                headers=self.config.get_headers(),
                timeout=timeout
            )
    
    async def close(self):
        """关闭HTTP会话"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """发起HTTP请求"""
        await self._ensure_session()
        
        url = f"{self.BASE_URL}{endpoint}"
        
        for attempt in range(self.config.max_retries + 1):
            try:
                logger.debug(f"请求 {method} {url}, 参数: {params}, 数据: {data}")
                
                async with self.session.request(
                    method, url, params=params, json=data, **kwargs
                ) as response:
                    response.raise_for_status()
                    
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        result = await response.json()
                    else:
                        text = await response.text()
                        try:
                            result = json.loads(text)
                        except json.JSONDecodeError:
                            result = {"text": text}
                    
                    logger.debug(f"响应: {result}")
                    return result
                    
            except Exception as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {str(e)}")
                
                if attempt == self.config.max_retries:
                    raise
                
                await asyncio.sleep(self.config.retry_delay * (attempt + 1))
    
    async def validate_connection(self):
        """验证API连接"""
        if not self.config.is_authenticated:
            raise ValueError("未配置微信读书认证信息")
        
        try:
            # 尝试获取用户信息
            await self._get_user_id()
            logger.info("微信读书API连接验证成功")
        except Exception as e:
            logger.error(f"微信读书API连接验证失败: {str(e)}")
            raise
    
    async def _get_user_id(self) -> str:
        """获取用户ID"""
        if self._user_id:
            return self._user_id
        
        if self.config.wechat_reading_user_id:
            self._user_id = self.config.wechat_reading_user_id
            return self._user_id
        
        # 从Cookie中提取用户ID
        cookie = self.config.wechat_reading_cookie
        if cookie:
            # 查找wr_vid参数
            match = re.search(r'wr_vid=(\d+)', cookie)
            if match:
                self._user_id = match.group(1)
                return self._user_id
        
        # 尝试通过API获取用户信息
        try:
            response = await self._make_request("GET", "/web/user")
            if "userVid" in response:
                self._user_id = str(response["userVid"])
                return self._user_id
        except Exception:
            pass
        
        raise ValueError("无法获取用户ID，请检查认证信息")
    
    async def get_book_list(self, status: str = "all", limit: int = 20) -> List[Dict[str, Any]]:
        """获取书籍列表"""
        logger.info(f"获取书籍列表: status={status}, limit={limit}")

        params = {
            "userVid": await self._get_user_id(),
            "count": limit,
            "mine": 1,
        }

        # 根据状态过滤
        if status == "reading":
            params["type"] = 1  # 在读
        elif status == "finished":
            params["type"] = 2  # 已读
        elif status == "wishlist":
            params["type"] = 3  # 想读

        # 尝试多个API端点
        endpoints_to_try = ["shelf", "shelf_book", "shelf_sync"]
        books = []

        for endpoint_name in endpoints_to_try:
            try:
                response = await self._make_request("GET", self.ENDPOINTS[endpoint_name], params=params)

                # 检查响应是否包含HTML（表示未正确认证）
                if "text" in response and "<html" in response.get("text", ""):
                    logger.warning(f"API端点 {endpoint_name} 返回HTML页面，尝试下一个端点")
                    continue

                # 尝试从不同的数据结构中提取书籍信息
                book_sources = [
                    response.get("books", []),
                    response.get("shelf", {}).get("books", []),
                    response.get("data", {}).get("books", []),
                    response.get("bookList", []),
                    response.get("items", [])
                ]

                for book_source in book_sources:
                    if book_source:
                        for book_data in book_source[:limit]:
                            try:
                                book = self._parse_book_info(book_data)
                                # 只添加有效的书籍信息（至少有书籍ID或标题）
                                if book.get("book_id") or book.get("title"):
                                    books.append(book)
                            except Exception as e:
                                logger.warning(f"解析书籍信息失败: {e}, 数据: {book_data}")
                                continue
                        break

                # 如果找到了书籍，就不再尝试其他端点
                if books:
                    logger.info(f"使用端点 {endpoint_name} 成功获取书籍")
                    break

            except Exception as e:
                logger.warning(f"API端点 {endpoint_name} 请求失败: {e}")
                continue

        # 如果所有API端点都失败，尝试从笔记中提取书籍信息作为备用方案
        if not books:
            logger.info("尝试从笔记中提取书籍信息作为备用方案")
            try:
                notes = await self.get_notes(limit=100)  # 获取更多笔记来提取书籍
                book_ids = set()

                for note in notes:
                    book_id = note.get("book_id")
                    if book_id and book_id not in book_ids:
                        book_ids.add(book_id)
                        try:
                            book_details = await self.get_book_details(book_id)
                            if book_details.get("title"):  # 确保书籍信息有效
                                books.append(book_details)
                                if len(books) >= limit:
                                    break
                        except Exception as e:
                            logger.warning(f"获取书籍详情失败 {book_id}: {e}")
                            continue

                logger.info(f"从笔记中提取到 {len(books)} 本书籍")
            except Exception as e:
                logger.warning(f"从笔记提取书籍信息失败: {e}")

        logger.info(f"最终获取到 {len(books)} 本书籍")
        return books
    
    async def get_book_details(self, book_id: str) -> Dict[str, Any]:
        """获取书籍详细信息"""
        logger.info(f"获取书籍详细信息: {book_id}")
        
        params = {"bookId": book_id}
        response = await self._make_request("GET", self.ENDPOINTS["book_info"], params=params)
        
        book_details = self._parse_book_details(response)
        
        # 获取阅读进度
        try:
            progress_response = await self._make_request(
                "GET", self.ENDPOINTS["progress"], params=params
            )
            if "progress" in progress_response:
                book_details["reading_progress"] = progress_response["progress"]
        except Exception as e:
            logger.warning(f"获取阅读进度失败: {str(e)}")
        
        return book_details

    async def get_notes(self, book_id: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """获取笔记和划线内容"""
        logger.info(f"获取笔记: book_id={book_id}, limit={limit}")

        params = {
            "userVid": await self._get_user_id(),
            "count": limit,
        }

        if book_id:
            params["bookId"] = book_id

        response = await self._make_request("GET", self.ENDPOINTS["notes"], params=params)

        notes = []
        if "updated" in response:
            for note_data in response["updated"][:limit]:
                note = self._parse_note_info(note_data)
                notes.append(note)

        logger.info(f"获取到 {len(notes)} 条笔记")
        return notes

    async def search_books(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索书籍"""
        logger.info(f"搜索书籍: query={query}, limit={limit}")

        params = {
            "keyword": query,
            "count": limit,
            "maxIdx": 0,
        }

        response = await self._make_request("GET", self.ENDPOINTS["search"], params=params)

        books = []

        # 检查响应是否包含HTML（表示未正确认证）
        if "text" in response and "<html" in response.get("text", ""):
            logger.warning("搜索API返回HTML页面，可能是Cookie已失效或权限不足")
            return books

        # 尝试从响应中提取书籍信息
        if "books" in response:
            for book_data in response["books"][:limit]:
                try:
                    book = self._parse_search_book_info(book_data)
                    # 只添加有效的书籍信息（至少有标题）
                    if book.get("title"):
                        books.append(book)
                except Exception as e:
                    logger.warning(f"解析搜索结果失败: {e}, 数据: {book_data}")
                    continue

        logger.info(f"搜索到 {len(books)} 本书籍")
        return books

    def _parse_book_info(self, book_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析书籍信息"""
        # 处理可能的嵌套结构
        if "bookInfo" in book_data:
            book_data = book_data["bookInfo"]

        return {
            "book_id": book_data.get("bookId", ""),
            "title": book_data.get("title", ""),
            "author": book_data.get("author", ""),
            "cover": book_data.get("cover", ""),
            "intro": book_data.get("intro", ""),
            "category": book_data.get("category", ""),
            "rating": book_data.get("newRating", 0) / 1000 if book_data.get("newRating") else 0,
            "rating_count": book_data.get("newRatingCount", 0),
            "word_count": book_data.get("totalWords", 0),
            "publish_time": book_data.get("publishTime", ""),
            "publisher": book_data.get("publisher", ""),
            "isbn": book_data.get("isbn", ""),
            "price": book_data.get("price", 0),
            "format": book_data.get("format", ""),
            "status": self._get_book_status(book_data),
            "last_read_time": book_data.get("lastReadTime", 0),
            "read_time": book_data.get("readTime", 0),
        }

    def _parse_book_details(self, book_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析书籍详细信息"""
        return {
            "book_id": book_data.get("bookId", ""),
            "title": book_data.get("title", ""),
            "author": book_data.get("author", ""),
            "cover": book_data.get("cover", ""),
            "intro": book_data.get("intro", ""),
            "category": book_data.get("category", ""),
            "rating": book_data.get("newRating", 0) / 1000 if book_data.get("newRating") else 0,
            "rating_count": book_data.get("newRatingCount", 0),
            "word_count": book_data.get("totalWords", 0),
            "publish_time": book_data.get("publishTime", ""),
            "publisher": book_data.get("publisher", ""),
            "isbn": book_data.get("isbn", ""),
            "price": book_data.get("price", 0),
            "format": book_data.get("format", ""),
            "chapter_count": book_data.get("chapterCount", 0),
            "tags": book_data.get("tags", []),
            "similar_books": book_data.get("similarBooks", []),
        }

    def _parse_search_book_info(self, book_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析搜索结果中的书籍信息"""
        # 搜索结果的数据结构可能嵌套在bookInfo中
        book_info = book_data.get("bookInfo", book_data)

        return {
            "book_id": book_info.get("bookId", ""),
            "title": book_info.get("title", ""),
            "author": book_info.get("author", ""),
            "cover": book_info.get("cover", ""),
            "intro": book_info.get("intro", ""),
            "category": book_info.get("category", ""),
            "rating": book_info.get("newRating", 0) / 1000 if book_info.get("newRating") else 0,
            "rating_count": book_info.get("newRatingCount", 0),
            "word_count": book_info.get("totalWords", 0),
            "publisher": book_info.get("publisher", ""),
            "price": book_info.get("price", 0),
        }

    def _parse_note_info(self, note_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析笔记信息"""
        return {
            "note_id": note_data.get("bookmarkId", ""),
            "book_id": note_data.get("bookId", ""),
            "book_title": note_data.get("bookTitle", ""),
            "chapter_title": note_data.get("chapterTitle", ""),
            "content": note_data.get("markText", ""),
            "note": note_data.get("review", ""),
            "create_time": note_data.get("createTime", 0),
            "type": note_data.get("type", 0),  # 0: 划线, 1: 笔记
            "color": note_data.get("colorStyle", 0),
            "page": note_data.get("range", ""),
        }

    def _get_book_status(self, book_data: Dict[str, Any]) -> str:
        """获取书籍状态"""
        if book_data.get("finishReading"):
            return "finished"
        elif book_data.get("readTime", 0) > 0:
            return "reading"
        else:
            return "wishlist"
